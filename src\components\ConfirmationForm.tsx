'use client'

import { useState } from 'react'
import { createGuest } from '@/lib/api'
import { sendGuestConfirmationEmail, debugEmailConfig } from '@/lib/emailService'
import { useNotification } from '@/hooks/useNotification'
import { User, Users, Check, Gift } from 'lucide-react'

interface ConfirmationFormProps {
  onSwitchToGifts?: () => void
}

export default function ConfirmationForm({ onSwitchToGifts }: ConfirmationFormProps) {
  const [name, setName] = useState('')
  const [totalPeople, setTotalPeople] = useState(1)
  const [totalPeopleInput, setTotalPeopleInput] = useState('1')
  const [companions, setCompanions] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [showGiftReminder, setShowGiftReminder] = useState(false)

  const { showSuccess, showError, NotificationComponent } = useNotification()

  const handleTotalPeopleChange = (value: string) => {
    // Atualizar o valor do input sempre
    setTotalPeopleInput(value)

    // Se campo vazio, manter estado interno como 1 mas permitir digitação
    if (value === '') {
      setTotalPeople(1)
      setCompanions([])
      return
    }

    const numValue = parseInt(value)

    // Validações
    if (isNaN(numValue) || numValue < 1) {
      setTotalPeople(1)
      setCompanions([])
      return
    }

    if (numValue > 10) {
      setTotalPeople(10)
      setTotalPeopleInput('10')
      const companionCount = 9
      setCompanions(prev => {
        const newCompanions = [...prev]
        while (newCompanions.length < companionCount) {
          newCompanions.push('')
        }
        return newCompanions.slice(0, companionCount)
      })
      return
    }

    setTotalPeople(numValue)
    const companionCount = numValue - 1
    setCompanions(Array(companionCount).fill(''))
  }

  const handleCompanionChange = (index: number, value: string) => {
    const newCompanions = [...companions]
    newCompanions[index] = value
    setCompanions(newCompanions)
  }

  const handleTotalPeopleBlur = () => {
    // Quando o usuário sai do campo, garantir que há um valor válido
    if (totalPeopleInput === '' || parseInt(totalPeopleInput) < 1) {
      setTotalPeopleInput('1')
      setTotalPeople(1)
      setCompanions([])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      await createGuest({
        name,
        total_people: totalPeople,
        companions: companions.filter(c => c.trim() !== '')
      })

      // Debug da configuração do email
      debugEmailConfig()

      // Enviar email de notificação
      try {
        console.log('Tentando enviar email de confirmação...')
        const emailResult = await sendGuestConfirmationEmail({
          guest_name: name,
          total_people: totalPeople,
          companions: companions.filter(c => c.trim() !== ''),
          confirmation_date: new Date().toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        })

        if (emailResult.success) {
          console.log('Email enviado com sucesso!')
        } else {
          console.error('Falha ao enviar email:', emailResult.error)
        }
      } catch (emailError) {
        console.error('Erro ao enviar email:', emailError)
        // Não falha a confirmação se o email falhar
      }

      setSuccess(true)
      setShowGiftReminder(true)
      // Reset form
      setName('')
      setTotalPeople(1)
      setTotalPeopleInput('1')
      setCompanions([])
      showSuccess('Presença Confirmada!', 'Obrigado por confirmar sua presença! Estamos ansiosos para te ver no nosso chá de panela! 💕')
    } catch (error) {
      showError('Erro ao Confirmar', 'Não foi possível confirmar sua presença. Tente novamente.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="flex justify-center mb-4">
          <Check className="w-16 h-16 text-green-500" />
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Presença Confirmada! 🎉
        </h2>
        <p className="text-gray-600 mb-4">
          Obrigado por confirmar sua presença no nosso chá de panela!
          Daiane & Delandi Lucas estão ansiosos para compartilhar este momento especial com você! 💕
        </p>
        <div className="bg-pink-50 border border-pink-200 rounded-lg p-4 mb-6">
          <p className="text-pink-800 font-medium text-center">
            📅 Lembre-se: <strong>26 de Julho às 19:30h</strong>
          </p>
        </div>

        {/* Lembrete para Lista de Presentes */}
        {showGiftReminder && (
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6 mb-6">
            <div className="text-center">
              <Gift className="w-8 h-8 text-purple-600 mx-auto mb-3" />
              <h3 className="text-lg font-bold text-purple-800 mb-2">
                🎁 Que tal escolher um presente?
              </h3>
              <p className="text-purple-700 mb-4 text-sm">
                Agora que você confirmou presença, não esqueça de escolher um presente especial para nós!
                Temos uma lista com várias opções lindas! 💕
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={() => {
                    setShowGiftReminder(false)
                    onSwitchToGifts?.()
                  }}
                  className="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors font-medium flex items-center justify-center"
                >
                  <Gift className="w-4 h-4 mr-2" />
                  Ver Lista de Presentes
                </button>
                <button
                  onClick={() => setShowGiftReminder(false)}
                  className="text-purple-600 hover:text-purple-800 px-4 py-2 text-sm font-medium"
                >
                  Talvez depois
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={() => {
              setSuccess(false)
              setName('')
              setTotalPeople(1)
              setTotalPeopleInput('1')
              setCompanions([])
              setShowGiftReminder(false)
            }}
            className="bg-pink-500 text-white px-6 py-2 rounded-lg hover:bg-pink-600 transition-colors"
          >
            Confirmar Outra Presença
          </button>
          {!showGiftReminder && onSwitchToGifts && (
            <button
              onClick={onSwitchToGifts}
              className="bg-purple-500 text-white px-6 py-2 rounded-lg hover:bg-purple-600 transition-colors flex items-center justify-center"
            >
              <Gift className="w-4 h-4 mr-2" />
              Ver Presentes
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-8">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
        Confirmar Presença
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Nome completo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <User className="w-4 h-4 inline mr-2" />
            Seu nome completo
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            placeholder="Digite seu nome completo"
          />
        </div>

        {/* Total de pessoas */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Users className="w-4 h-4 inline mr-2" />
            Total de pessoas (incluindo você)
          </label>
          <input
            type="number"
            min="1"
            max="10"
            value={totalPeopleInput}
            onChange={(e) => handleTotalPeopleChange(e.target.value)}
            onBlur={handleTotalPeopleBlur}
            required
            placeholder="Digite o número de pessoas"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-center text-lg font-medium"
            style={{
              WebkitAppearance: 'none',
              MozAppearance: 'textfield'
            }}
          />
        </div>

        {/* Acompanhantes */}
        {totalPeople > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome dos acompanhantes
            </label>
            <div className="space-y-3">
              {companions.map((companion, index) => (
                <input
                  key={index}
                  type="text"
                  value={companion}
                  onChange={(e) => handleCompanionChange(index, e.target.value)}
                  placeholder={`Nome do acompanhante ${index + 1}`}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
              ))}
            </div>
          </div>
        )}

        {/* Botão de submit */}
        <button
          type="submit"
          disabled={loading || !name.trim()}
          className="w-full bg-pink-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-pink-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? 'Confirmando...' : 'Confirmar Presença'}
        </button>
      </form>

      {/* Componente de Notificação */}
      <NotificationComponent />
    </div>
  )
}
