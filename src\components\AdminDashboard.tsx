'use client'

import { useState, useEffect } from 'react'
import { Guest, Gift } from '@/types'
import { getGuests, getGifts } from '@/lib/api'
import { LogOut, Users, Gift as GiftIcon, Settings, Palette } from 'lucide-react'
import GuestsList from './GuestsList'
import GiftsManagement from './GiftsManagement'
import SiteCustomization from './SiteCustomization'
import ReservedGifts from './ReservedGifts'

interface AdminDashboardProps {
  onLogout: () => void
}

export default function AdminDashboard({ onLogout }: AdminDashboardProps) {
  const [guests, setGuests] = useState<Guest[]>([])
  const [gifts, setGifts] = useState<Gift[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'guests' | 'gifts' | 'reserved' | 'customization'>('guests')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [guestsData, giftsData] = await Promise.all([
        getGuests(),
        getGifts()
      ])
      setGuests(guestsData)
      setGifts(giftsData)
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDataUpdate = () => {
    loadData()
  }

  const reservedGifts = gifts.filter(gift => gift.is_reserved)
  const totalGuests = guests.reduce((sum, guest) => sum + guest.total_people, 0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                Painel Administrativo
              </h1>
              <p className="text-sm text-gray-600">
                Chá de Panela - Daiane & Delandi Lucas
              </p>
            </div>
            <button
              onClick={onLogout}
              className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sair
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Convidados</p>
                <p className="text-2xl font-bold text-gray-900">{guests.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Pessoas</p>
                <p className="text-2xl font-bold text-gray-900">{totalGuests}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <GiftIcon className="w-8 h-8 text-pink-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Presentes Reservados</p>
                <p className="text-2xl font-bold text-gray-900">{reservedGifts.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('guests')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'guests'
                    ? 'border-pink-500 text-pink-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Users className="w-4 h-4 inline mr-2" />
                Convidados Confirmados
              </button>
              <button
                onClick={() => setActiveTab('gifts')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'gifts'
                    ? 'border-pink-500 text-pink-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Settings className="w-4 h-4 inline mr-2" />
                Gerenciar Presentes
              </button>
              <button
                onClick={() => setActiveTab('reserved')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'reserved'
                    ? 'border-pink-500 text-pink-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <GiftIcon className="w-4 h-4 inline mr-2" />
                Presentes Reservados
              </button>
              <button
                onClick={() => setActiveTab('customization')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'customization'
                    ? 'border-pink-500 text-pink-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Palette className="w-4 h-4 inline mr-2" />
                Personalização
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Carregando dados...</p>
              </div>
            ) : (
              <>
                {activeTab === 'guests' && <GuestsList guests={guests} onUpdate={handleDataUpdate} />}
                {activeTab === 'gifts' && (
                  <GiftsManagement gifts={gifts} onUpdate={handleDataUpdate} />
                )}
                {activeTab === 'reserved' && (
                  <ReservedGifts gifts={reservedGifts} onUpdate={handleDataUpdate} />
                )}
                {activeTab === 'customization' && (
                  <SiteCustomization onUpdate={handleDataUpdate} />
                )}
              </>
            )}
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-8 py-6 border-t border-gray-200 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <p className="text-gray-500 text-sm">
                Sistema desenvolvido por{' '}
                <span className="font-bold text-gray-700">DLTECHNOLOGY</span>
              </p>
              <p className="text-gray-400 text-xs mt-1">
                Criando soluções digitais para momentos especiais ✨
              </p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}
