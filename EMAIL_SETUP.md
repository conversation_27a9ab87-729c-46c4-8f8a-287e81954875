# 📧 Configuração do Sistema de Email (EmailJS)

## 🎯 **Visão Geral**
Sistema de notificações por email **100% GRATUITO** usando EmailJS para receber avisos quando:
- ✅ Alguém confirmar presença
- 🎁 Alguém reservar um presente

## 🚀 **Passo a Passo para Configurar**

### 1. **Criar Conta no EmailJS**
1. Acesse: [https://www.emailjs.com/](https://www.emailjs.com/)
2. Clique em **"Sign Up"**
3. Crie sua conta gratuita (200 emails/mês grátis)

### 2. **Configurar Serviço de Email**
1. No dashboard, clique em **"Email Services"**
2. Clique em **"Add New Service"**
3. Escolha **"Gmail"** (recomendado)
4. Conecte sua conta Gmail
5. Anote o **Service ID** (ex: `service_abc123`) service_er3k78o

### 3. **<PERSON><PERSON>r Templates de Email**

#### 📋 **Template 1: Confirmação de Presença**
1. Vá em **"Email Templates"**
2. Clique em **"Create New Template"**
3. **Template ID:** `template_guest_confirmation`
4. **Assunto:** `🎉 Nova Confirmação - Chá de Panela Daiane & Delandi`
5. **Para (To):** Seu email (ex: `<EMAIL>`)
6. **Conteúdo:**
```html
<h2>🎉 Nova Confirmação de Presença!</h2>

<p><strong>Convidado:</strong> {{guest_name}}</p>
<p><strong>Total de Pessoas:</strong> {{total_people}}</p>
<p><strong>Acompanhantes:</strong> {{companions}}</p>
<p><strong>Data da Confirmação:</strong> {{confirmation_date}}</p>

<hr>
<p><strong>📅 Evento:</strong> {{event_date}}</p>
<p><strong>📍 Local:</strong> {{event_location}}</p>

<p>💕 <em>Mais um convidado confirmado para o seu chá de panela!</em></p>
```

**⚠️ IMPORTANTE:** No campo **"To"** do template, coloque seu email fixo (ex: `<EMAIL>`)

#### 🎁 **Template 2: Reserva de Presente**
1. Criar novo template
2. **Template ID:** `template_gift_reservation`
3. **Assunto:** `🎁 Presente Reservado - Chá de Panela Daiane & Delandi`
4. **Para (To):** Seu email (ex: `<EMAIL>`)
5. **Conteúdo:**
```html
<h2>🎁 Novo Presente Reservado!</h2>

<p><strong>Presente:</strong> {{gift_name}}</p>
<p><strong>Reservado por:</strong> {{reserved_by}}</p>
<p><strong>Mensagem:</strong> {{message}}</p>
<p><strong>Data da Reserva:</strong> {{reservation_date}}</p>

<hr>
<p><strong>📅 Evento:</strong> {{event_date}}</p>

<p>🎉 <em>Mais um presente garantido para vocês!</em></p>
```

**⚠️ IMPORTANTE:** No campo **"To"** do template, coloque seu email fixo (ex: `<EMAIL>`)

### 4. **Obter Chave Pública**
1. Vá em **"Account"** → **"General"**
2. Copie a **"Public Key"** (ex: `user_abc123xyz`)

### 5. **Configurar Variáveis de Ambiente**
Edite o arquivo `.env.local` com suas configurações:

```env
# EmailJS Configuration
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_abc123
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GUEST=template_guest_confirmation
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GIFT=template_gift_reservation
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=user_abc123xyz
```

### 6. **Testar Configuração**
1. Faça uma confirmação de presença no site
2. Reserve um presente
3. Verifique se os emails chegaram em `<EMAIL>`

## 🎯 **Recursos Incluídos**

### 📧 **Emails Automáticos:**
- **Confirmação de Presença:** Nome, quantidade de pessoas, acompanhantes
- **Reserva de Presente:** Nome do presente, quem reservou, mensagem
- **Informações do Evento:** Data, horário e local sempre incluídos

### 🔒 **Segurança:**
- **Não falha:** Se email falhar, confirmação/reserva ainda funciona
- **Logs:** Erros registrados no console para debug
- **Privacidade:** Apenas você recebe os emails

### 💰 **Custo:**
- **100% GRATUITO** até 200 emails/mês
- **Sem cartão de crédito** necessário
- **Sem taxas ocultas**

## 🛠️ **Troubleshooting**

### ❌ **Erro 422 "The recipients address is empty":**

#### 🔧 **Soluções Passo a Passo:**

1. **Configuração do Template EmailJS:**
   - Acesse [EmailJS Dashboard](https://dashboard.emailjs.com/)
   - Vá em "Email Templates"
   - Edite o template `template_guest_confirmation`
   - **IMPORTANTE:** No campo "To Email", coloque: `<EMAIL>`
   - **NÃO use** `{{to_email}}` ou outras variáveis no campo "To"
   - Salve o template

2. **Configuração do Serviço:**
   - Vá em "Email Services"
   - Verifique se o serviço Gmail está ativo
   - Teste a conexão

3. **Verificar Variáveis de Ambiente:**
   ```env
   NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_xxxxxxx
   NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GUEST=template_guest_confirmation
   NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GIFT=template_gift_reservation
   NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=user_xxxxxxxxxxxxxxx
   ```

4. **Debug no Console:**
   - Abra F12 no navegador
   - Vá na aba "Console"
   - Confirme uma presença
   - Procure por logs de debug do EmailJS

### ❌ **Emails não chegam:**
1. Verifique se as variáveis de ambiente estão corretas
2. Confirme se os Template IDs estão exatos
3. Verifique spam/lixo eletrônico
4. Teste com outro email

### ⚠️ **Limite de emails:**
- **Plano gratuito:** 200 emails/mês
- **Para mais:** Upgrade para plano pago (muito barato)
- **Monitoramento:** Dashboard do EmailJS mostra uso

## 📱 **Como Funciona**

### 🔄 **Fluxo Automático:**
1. **Convidado confirma presença** → Email enviado automaticamente
2. **Convidado reserva presente** → Email enviado automaticamente
3. **Você recebe notificação** → Fica sabendo na hora!

### 📊 **Informações Incluídas:**
- **Dados completos** do convidado/reserva
- **Data e horário** da ação
- **Informações do evento** para contexto
- **Formatação bonita** e profissional

## 🎉 **Pronto!**
Após configurar, você receberá emails automáticos para cada confirmação e reserva! 📧✨
