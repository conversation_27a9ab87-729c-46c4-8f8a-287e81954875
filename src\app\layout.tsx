import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '<PERSON><PERSON> de Panela - <PERSON> & <PERSON><PERSON><PERSON>',
  description: 'Confirme sua presença e escolha um presente especial para o chá de panela da Daiane & Delandi Lucas',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
