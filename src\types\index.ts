export interface Guest {
  id: string;
  name: string;
  total_people: number;
  companions: string[];
  created_at: string;
}

export interface Gift {
  id: string;
  name: string;
  description?: string;
  image_url?: string;
  purchase_url?: string;
  is_reserved: boolean;
  reserved_by?: string;
  reserved_at?: string;
  message?: string;
  created_at: string;
}

export interface GiftReservation {
  gift_id: string;
  reserved_by: string;
  message?: string;
}

export interface SiteCustomization {
  id: string;
  background_images: string[];
  background_style: 'slideshow' | 'static' | 'parallax';
  background_size: 'cover' | 'contain' | 'auto';
  overlay_opacity: number;
  primary_color: string;
  secondary_color: string;
  created_at: string;
  updated_at: string;
}
