'use client'

import { Guest } from '@/types'
import { deleteGuest } from '@/lib/api'
import { useNotification } from '@/hooks/useNotification'
import { Download, User, Users, Trash2 } from 'lucide-react'
import * as XLSX from 'xlsx'

interface GuestsListProps {
  guests: Guest[]
  onUpdate: () => void
}

export default function GuestsList({ guests, onUpdate }: GuestsListProps) {
  const { showSuccess, showError, showWarning, NotificationComponent } = useNotification()

  const exportToExcel = () => {
    const data = guests.map(guest => ({
      'Nome do Convidado': guest.name,
      'Total de Pessoas': guest.total_people,
      'Acompanhantes': guest.companions.join(', ') || 'Nenhum',
      'Data de Confirmação': new Date(guest.created_at).toLocaleDateString('pt-BR')
    }))

    const ws = XLSX.utils.json_to_sheet(data)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Convidados')
    
    XLSX.writeFile(wb, `convidados_cha_de_panela_${new Date().toISOString().split('T')[0]}.xlsx`)
  }

  const handleDeleteGuest = async (guest: Guest) => {
    if (confirm(`Tem certeza que deseja excluir a confirmação de "${guest.name}"?`)) {
      try {
        await deleteGuest(guest.id)
        onUpdate()
        showSuccess('Confirmação Excluída!', `A confirmação de "${guest.name}" foi excluída com sucesso.`)
      } catch (error) {
        showError('Erro ao Excluir', 'Não foi possível excluir a confirmação. Tente novamente.')
      }
    }
  }

  if (guests.length === 0) {
    return (
      <div className="text-center py-8">
        <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Nenhum convidado confirmou presença ainda.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          Lista de Convidados Confirmados
        </h3>
        <button
          onClick={exportToExcel}
          className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
        >
          <Download className="w-4 h-4 mr-2" />
          Exportar para Excel
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nome do Convidado
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total de Pessoas
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Acompanhantes
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Data de Confirmação
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ações
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {guests.map((guest) => (
              <tr key={guest.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <User className="w-5 h-5 text-gray-400 mr-3" />
                    <div className="text-sm font-medium text-gray-900">
                      {guest.name}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Users className="w-5 h-5 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-900">
                      {guest.total_people}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    {guest.companions.length > 0 ? (
                      <ul className="list-disc list-inside">
                        {guest.companions.map((companion, index) => (
                          <li key={index}>{companion}</li>
                        ))}
                      </ul>
                    ) : (
                      <span className="text-gray-500 italic">Nenhum acompanhante</span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(guest.created_at).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => handleDeleteGuest(guest)}
                    className="text-red-600 hover:text-red-900 hover:bg-red-50 p-2 rounded-lg transition-colors"
                    title="Excluir confirmação"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Componente de Notificação */}
      <NotificationComponent />
    </div>
  )
}
