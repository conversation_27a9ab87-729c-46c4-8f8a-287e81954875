{"name": "cha-de-panela", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "deploy": "npm run build && firebase deploy"}, "dependencies": {"@emailjs/browser": "^4.4.1", "firebase": "^10.14.1", "lucide-react": "^0.395.0", "next": "14.2.4", "react": "^18", "react-dom": "^18", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}