# 🚀 Setup Rápido - Firebase

## ⚡ Configuração em 5 Minutos

### 1. Criar Projeto Firebase
```bash
# Acesse: https://console.firebase.google.com
# Clique em "Criar um projeto"
# Nome: cha-de-panela (ou outro nome)
```

### 2. Ativar Serviços
- ✅ **Firestore Database** → "Criar banco de dados" → "Modo de teste"
- ✅ **Hosting** → "Começar"

### 3. Obter Credenciais
```bash
# No console Firebase:
# Configurações do projeto → Geral → Seus aplicativos → Web
# Copie as credenciais de configuração
```

### 4. Configurar Localmente
```bash
# Instalar Firebase CLI
npm install -g firebase-tools

# Login
firebase login

# Clonar/baixar o projeto
# Instalar dependências
npm install

# Configurar variáveis de ambiente
cp .env.local.example .env.local
# Edite .env.local com suas credenciais Firebase
```

### 5. Configurar Firebase
```bash
# Editar .firebaserc com seu project-id
# Aplicar regras do Firestore
firebase deploy --only firestore:rules

# Testar localmente
npm run dev
```

### 6. Deploy
```bash
# Build e deploy
npm run deploy

# Ou manualmente
npm run build
firebase deploy --only hosting
```

## 🔧 Arquivo .env.local
```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSy...
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=seu-projeto.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=seu-projeto-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=seu-projeto.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123:web:abc123
ADMIN_PASSWORD=sua_senha_admin
```

## 🎯 URLs Importantes
- **Console Firebase**: https://console.firebase.google.com
- **Sua App**: https://seu-projeto-id.web.app
- **Admin**: https://seu-projeto-id.web.app/admin

## 📱 Testar
1. Acesse a URL da sua app
2. Confirme uma presença
3. Reserve um presente
4. Acesse /admin com a senha
5. Gerencie presentes e exporte dados

## 🆘 Problemas Comuns
- **Erro de permissão**: Execute `firebase deploy --only firestore:rules`
- **Variáveis não carregam**: Reinicie `npm run dev`
- **Build falha**: Verifique se todas as env vars estão configuradas

## 📊 Dados de Exemplo
```bash
# Para adicionar presentes de exemplo:
node firestore-seed.js
```

## ✅ Checklist Final
- [ ] Projeto Firebase criado
- [ ] Firestore ativado
- [ ] Hosting ativado
- [ ] Credenciais copiadas
- [ ] .env.local configurado
- [ ] .firebaserc atualizado
- [ ] Regras aplicadas
- [ ] App funcionando localmente
- [ ] Deploy realizado
- [ ] Testes completos

🎉 **Pronto!** Sua aplicação está no ar!
