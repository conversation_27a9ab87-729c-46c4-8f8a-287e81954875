#!/bin/bash

# Script de deploy para Firebase Hosting
echo "🚀 Iniciando deploy para Firebase..."

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
  echo "📦 Instalando dependências..."
  npm install
fi

# Build da aplicação
echo "🔨 Fazendo build da aplicação..."
npm run build

# Deploy para Firebase
echo "☁️ Fazendo deploy para Firebase Hosting..."
firebase deploy --only hosting

echo "✅ Deploy concluído com sucesso!"
echo "🌐 Sua aplicação está disponível em: https://your-project-id.web.app"
