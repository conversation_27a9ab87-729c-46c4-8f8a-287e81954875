import { db } from './firebase'
import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp
} from 'firebase/firestore'
import { Guest, Gift, GiftReservation, SiteCustomization } from '@/types'

// Guest functions
export async function createGuest(guestData: Omit<Guest, 'id' | 'created_at'>) {
  try {
    const docRef = await addDoc(collection(db, 'guests'), {
      ...guestData,
      created_at: Timestamp.now()
    })

    return {
      id: docRef.id,
      ...guestData,
      created_at: new Date().toISOString()
    }
  } catch (error) {
    console.error('Error creating guest:', error)
    throw error
  }
}

export async function getGuests(): Promise<Guest[]> {
  try {
    const q = query(collection(db, 'guests'), orderBy('created_at', 'desc'))
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      created_at: doc.data().created_at?.toDate?.()?.toISOString() || new Date().toISOString()
    })) as Guest[]
  } catch (error) {
    console.error('Error getting guests:', error)
    throw error
  }
}

export async function deleteGuest(guestId: string) {
  try {
    await deleteDoc(doc(db, 'guests', guestId))
  } catch (error) {
    console.error('Error deleting guest:', error)
    throw error
  }
}

// Gift functions
export async function getGifts(): Promise<Gift[]> {
  try {
    const q = query(collection(db, 'gifts'), orderBy('created_at', 'asc'))
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      created_at: doc.data().created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      reserved_at: doc.data().reserved_at?.toDate?.()?.toISOString() || undefined
    })) as Gift[]
  } catch (error) {
    console.error('Error getting gifts:', error)
    throw error
  }
}

export async function createGift(giftData: Omit<Gift, 'id' | 'created_at' | 'is_reserved' | 'reserved_by' | 'reserved_at' | 'message'>) {
  try {
    const docRef = await addDoc(collection(db, 'gifts'), {
      ...giftData,
      is_reserved: false,
      reserved_by: null,
      reserved_at: null,
      message: null,
      created_at: Timestamp.now()
    })

    return {
      id: docRef.id,
      ...giftData,
      is_reserved: false,
      created_at: new Date().toISOString()
    }
  } catch (error) {
    console.error('Error creating gift:', error)
    throw error
  }
}

export async function updateGift(id: string, giftData: Partial<Gift>) {
  try {
    const giftRef = doc(db, 'gifts', id)
    await updateDoc(giftRef, giftData)

    return {
      id,
      ...giftData
    }
  } catch (error) {
    console.error('Error updating gift:', error)
    throw error
  }
}

export async function deleteGift(id: string) {
  try {
    await deleteDoc(doc(db, 'gifts', id))
  } catch (error) {
    console.error('Error deleting gift:', error)
    throw error
  }
}

export async function reserveGift(reservation: GiftReservation) {
  try {
    const giftRef = doc(db, 'gifts', reservation.gift_id)
    await updateDoc(giftRef, {
      is_reserved: true,
      reserved_by: reservation.reserved_by,
      reserved_at: Timestamp.now(),
      message: reservation.message || null
    })

    return {
      id: reservation.gift_id,
      is_reserved: true,
      reserved_by: reservation.reserved_by,
      reserved_at: new Date().toISOString(),
      message: reservation.message
    }
  } catch (error) {
    console.error('Error reserving gift:', error)
    throw error
  }
}

export async function cancelReservation(giftId: string) {
  try {
    const giftRef = doc(db, 'gifts', giftId)
    await updateDoc(giftRef, {
      is_reserved: false,
      reserved_by: null,
      reserved_at: null,
      message: null
    })

    return {
      id: giftId,
      is_reserved: false
    }
  } catch (error) {
    console.error('Error canceling reservation:', error)
    throw error
  }
}

// Site Customization functions
export async function getSiteCustomization(): Promise<SiteCustomization | null> {
  try {
    const q = query(collection(db, 'site_customization'), orderBy('updated_at', 'desc'))
    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    return {
      id: doc.id,
      ...doc.data(),
      created_at: doc.data().created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: doc.data().updated_at?.toDate?.()?.toISOString() || new Date().toISOString()
    } as SiteCustomization
  } catch (error) {
    console.error('Error getting site customization:', error)
    throw error
  }
}

export async function updateSiteCustomization(customization: Omit<SiteCustomization, 'id' | 'created_at' | 'updated_at'>) {
  try {
    // Primeiro, tenta buscar uma customização existente
    const existing = await getSiteCustomization()

    if (existing) {
      // Atualiza a existente
      const customRef = doc(db, 'site_customization', existing.id)
      await updateDoc(customRef, {
        ...customization,
        updated_at: Timestamp.now()
      })

      return {
        id: existing.id,
        ...customization,
        created_at: existing.created_at,
        updated_at: new Date().toISOString()
      }
    } else {
      // Cria uma nova
      const docRef = await addDoc(collection(db, 'site_customization'), {
        ...customization,
        created_at: Timestamp.now(),
        updated_at: Timestamp.now()
      })

      return {
        id: docRef.id,
        ...customization,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Error updating site customization:', error)
    throw error
  }
}


