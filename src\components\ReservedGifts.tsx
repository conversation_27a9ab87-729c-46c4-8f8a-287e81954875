'use client'

import { Gift } from '@/types'
import { cancelReservation } from '@/lib/api'
import { useNotification } from '@/hooks/useNotification'
import { Download, Gift as GiftIcon, X, MessageSquare, Calendar, User } from 'lucide-react'
import * as XLSX from 'xlsx'

interface ReservedGiftsProps {
  gifts: Gift[]
  onUpdate: () => void
}

export default function ReservedGifts({ gifts, onUpdate }: ReservedGiftsProps) {
  const { showSuccess, showError, showWarning, NotificationComponent } = useNotification()

  const exportToExcel = () => {
    const data = gifts.map(gift => ({
      'Nome do Presente': gift.name,
      'Reservado por': gift.reserved_by || '',
      'Data da Reserva': gift.reserved_at ? new Date(gift.reserved_at).toLocaleDateString('pt-BR') : '',
      'Mensagem': gift.message || 'Nenhuma mensagem',
      'Descrição': gift.description || ''
    }))

    const ws = XLSX.utils.json_to_sheet(data)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Presentes Reservados')
    
    XLSX.writeFile(wb, `presentes_reservados_${new Date().toISOString().split('T')[0]}.xlsx`)
  }

  const handleCancelReservation = async (gift: Gift) => {
    if (confirm(`Tem certeza que deseja cancelar a reserva do presente "${gift.name}"?`)) {
      try {
        await cancelReservation(gift.id)
        onUpdate()
        showSuccess('Reserva Cancelada!', `A reserva do presente "${gift.name}" foi cancelada com sucesso.`)
      } catch (error) {
        showError('Erro ao Cancelar', 'Não foi possível cancelar a reserva. Tente novamente.')
      }
    }
  }

  if (gifts.length === 0) {
    return (
      <div className="text-center py-8">
        <GiftIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Nenhum presente foi reservado ainda.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          Presentes Reservados
        </h3>
        <button
          onClick={exportToExcel}
          className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
        >
          <Download className="w-4 h-4 mr-2" />
          Exportar para Excel
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {gifts.map((gift) => (
          <div
            key={gift.id}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
          >
            {gift.image_url && (
              <img
                src={gift.image_url}
                alt={gift.name}
                className="w-full h-32 object-cover rounded-lg mb-4"
              />
            )}
            
            <h4 className="font-semibold text-lg text-gray-800 mb-2">
              {gift.name}
            </h4>
            
            {gift.description && (
              <p className="text-gray-600 text-sm mb-3">
                {gift.description}
              </p>
            )}
            
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-gray-600">
                <User className="w-4 h-4 mr-2" />
                <span className="font-medium">Reservado por:</span>
                <span className="ml-1">{gift.reserved_by}</span>
              </div>
              
              {gift.reserved_at && (
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span className="font-medium">Data:</span>
                  <span className="ml-1">
                    {new Date(gift.reserved_at).toLocaleDateString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              )}
              
              {gift.message && (
                <div className="flex items-start text-sm text-gray-600">
                  <MessageSquare className="w-4 h-4 mr-2 mt-0.5" />
                  <div>
                    <span className="font-medium">Mensagem:</span>
                    <p className="mt-1 text-gray-700 italic">&ldquo;{gift.message}&rdquo;</p>
                  </div>
                </div>
              )}
            </div>
            
            <button
              onClick={() => handleCancelReservation(gift)}
              className="w-full flex items-center justify-center px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 transition-colors"
            >
              <X className="w-4 h-4 mr-2" />
              Cancelar Reserva
            </button>
          </div>
        ))}
      </div>

      {/* Componente de Notificação */}
      <NotificationComponent />
    </div>
  )
}
