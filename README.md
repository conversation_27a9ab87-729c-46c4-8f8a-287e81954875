# Chá de Panela - Sistema de Confirmação e Lista de Presentes

Uma aplicação web moderna para gerenciar confirmações de presença e lista de presentes para eventos de Chá de Panela.

## 🎯 Funcionalidades

### Página do Convidado
- ✅ Formulário de confirmação de presença
- ✅ Campos dinâmicos para acompanhantes
- ✅ Lista de presentes disponíveis
- ✅ Sistema de reserva de presentes com modal
- ✅ Interface responsiva e intuitiva

### Painel Administrativo
- ✅ Login protegido por senha
- ✅ Dashboard com estatísticas
- ✅ Gerenciamento da lista de presentes
- ✅ Visualização de convidados confirmados
- ✅ Lista de presentes reservados
- ✅ Exportação para Excel
- ✅ Cancelamento de reservas

## 🚀 Tecnologias Utilizadas

- **Next.js 14** - Framework React
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Estilização
- **Firebase** - Banco de dados (Firestore) e hospedagem
- **Lucide React** - Ícones
- **XLSX** - Exportação para Excel

## 📋 Pré-requisitos

- Node.js 18+
- Conta no Firebase (gratuita)
- Firebase CLI instalado globalmente: `npm install -g firebase-tools`

## 🛠️ Instalação

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd cha-de-panela
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure o Firebase**
   - Crie uma conta em [firebase.google.com](https://firebase.google.com)
   - Crie um novo projeto
   - Ative o Firestore Database
   - Ative o Firebase Hosting
   - Vá para Configurações do projeto > Geral e copie as credenciais

4. **Configure as variáveis de ambiente**
```bash
cp .env.local.example .env.local
```

Edite o arquivo `.env.local`:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=sua_api_key_do_firebase
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=seu_projeto.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=seu_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=seu_projeto.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=seu_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=seu_app_id
ADMIN_PASSWORD=sua_senha_admin
```

5. **Configure o Firebase CLI**
```bash
firebase login
firebase init
```
   - Selecione Firestore e Hosting
   - Use as configurações existentes (firebase.json)
   - Atualize o `.firebaserc` com seu project ID

6. **Execute o projeto**
```bash
npm run dev
```

A aplicação estará disponível em `http://localhost:3000`

## 📱 Como Usar

### Para Convidados
1. Acesse o link principal da aplicação
2. Use as abas para navegar entre "Confirmar Presença" e "Lista de Presentes"
3. Preencha o formulário de confirmação com seu nome e número de acompanhantes
4. Na lista de presentes, clique em "Quero dar este!" para reservar um item

### Para Administradores
1. Acesse `/admin`
2. Digite a senha configurada no `.env.local`
3. Use as abas para:
   - Ver lista de convidados confirmados
   - Gerenciar presentes (adicionar, editar, excluir)
   - Ver presentes reservados e cancelar reservas
   - Exportar dados para Excel

## 🎨 Personalização

### Cores e Tema
Edite o arquivo `tailwind.config.ts` para personalizar as cores:
```typescript
colors: {
  primary: {
    // Suas cores personalizadas
  }
}
```

### Textos e Conteúdo
- Título e descrições: `src/app/page.tsx`
- Textos do painel admin: `src/components/AdminDashboard.tsx`

## 📊 Estrutura do Banco de Dados (Firestore)

### Coleção `guests`
- `id` (string) - ID do documento
- `name` (string) - Nome do convidado
- `total_people` (number) - Total de pessoas
- `companions` (array) - Lista de acompanhantes
- `created_at` (timestamp) - Data de criação

### Coleção `gifts`
- `id` (string) - ID do documento
- `name` (string) - Nome do presente
- `description` (string) - Descrição opcional
- `image_url` (string) - URL da imagem
- `is_reserved` (boolean) - Se está reservado
- `reserved_by` (string) - Quem reservou
- `reserved_at` (timestamp) - Quando foi reservado
- `message` (string) - Mensagem do presenteador
- `created_at` (timestamp) - Data de criação

## 🔒 Segurança

- Painel administrativo protegido por senha
- Regras de segurança do Firestore configuradas
- Validação de dados no frontend e backend

## 📱 Responsividade

A aplicação é totalmente responsiva e funciona bem em:
- 📱 Smartphones
- 📱 Tablets
- 💻 Desktops

## 🚀 Deploy

### Firebase Hosting (Recomendado)
```bash
# Build e deploy automático
npm run deploy

# Ou manualmente
npm run build
firebase deploy --only hosting
```

### Outras Plataformas
A aplicação também pode ser deployada em:
- Vercel
- Netlify
- Qualquer plataforma que suporte Next.js com export estático

## 📞 Suporte

Para dúvidas ou problemas, verifique:
1. Se todas as variáveis de ambiente estão configuradas
2. Se o banco de dados foi criado corretamente
3. Se as dependências foram instaladas

## 📄 Licença

Este projeto está sob a licença MIT.
