import emailjs from '@emailjs/browser'

// Configurações do EmailJS (você precisará configurar no site do EmailJS)
const EMAILJS_SERVICE_ID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'your_service_id'
const EMAILJS_TEMPLATE_ID_GUEST = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GUEST || 'template_guest'
const EMAILJS_TEMPLATE_ID_GIFT = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_GIFT || 'template_gift'
const EMAILJS_PUBLIC_KEY = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'your_public_key'

// Inicializar EmailJS
emailjs.init(EMAILJS_PUBLIC_KEY)

export interface GuestNotificationData {
  guest_name: string
  total_people: number
  companions: string[]
  confirmation_date: string
}

export interface GiftNotificationData {
  gift_name: string
  reserved_by: string
  message?: string
  reservation_date: string
}

export const sendGuestConfirmationEmail = async (data: GuestNotificationData) => {
  try {
    // Verificar se as configurações estão definidas
    if (!EMAILJS_SERVICE_ID || !EMAILJS_TEMPLATE_ID_GUEST || !EMAILJS_PUBLIC_KEY) {
      console.error('EmailJS não configurado. Verifique as variáveis de ambiente.')
      return { success: false, error: 'EmailJS não configurado' }
    }

    const templateParams = {
      to_name: 'Delandi Lucas',
      to_email: '<EMAIL>', // Adicionando ambos os campos
      from_name: 'Sistema Chá de Panela',
      reply_to: '<EMAIL>',
      guest_name: data.guest_name,
      total_people: data.total_people.toString(),
      companions: data.companions.length > 0 ? data.companions.join(', ') : 'Nenhum acompanhante',
      confirmation_date: data.confirmation_date,
      event_date: '26 de Julho às 19:30h',
      event_location: 'Residencial Hiate, nº 77 - CEP 29303-370'
    }

    console.log('Enviando email com parâmetros:', templateParams)
    console.log('Service ID:', EMAILJS_SERVICE_ID)
    console.log('Template ID:', EMAILJS_TEMPLATE_ID_GUEST)

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID_GUEST,
      templateParams,
      EMAILJS_PUBLIC_KEY
    )

    console.log('Email de confirmação enviado com sucesso:', response.status, response.text)
    return { success: true, response }
  } catch (error) {
    console.error('Erro detalhado ao enviar email de confirmação:', error)
    return { success: false, error }
  }
}

export const sendGiftReservationEmail = async (data: GiftNotificationData) => {
  try {
    // Verificar se as configurações estão definidas
    if (!EMAILJS_SERVICE_ID || !EMAILJS_TEMPLATE_ID_GIFT || !EMAILJS_PUBLIC_KEY) {
      console.error('EmailJS não configurado. Verifique as variáveis de ambiente.')
      return { success: false, error: 'EmailJS não configurado' }
    }

    const templateParams = {
      to_name: 'Delandi Lucas',
      to_email: '<EMAIL>', // Adicionando ambos os campos
      from_name: 'Sistema Chá de Panela',
      reply_to: '<EMAIL>',
      gift_name: data.gift_name,
      reserved_by: data.reserved_by,
      message: data.message || 'Nenhuma mensagem',
      reservation_date: data.reservation_date,
      event_date: '26 de Julho às 19:30h'
    }

    console.log('Enviando email de presente com parâmetros:', templateParams)

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID_GIFT,
      templateParams,
      EMAILJS_PUBLIC_KEY
    )

    console.log('Email de reserva enviado com sucesso:', response.status, response.text)
    return { success: true, response }
  } catch (error) {
    console.error('Erro detalhado ao enviar email de reserva:', error)
    return { success: false, error }
  }
}

// Função para testar a configuração do email
export const testEmailConfiguration = async () => {
  try {
    console.log('Testando configuração do EmailJS...')
    console.log('Service ID:', EMAILJS_SERVICE_ID)
    console.log('Public Key:', EMAILJS_PUBLIC_KEY)

    const testParams = {
      to_name: 'Delandi Lucas',
      to_email: '<EMAIL>',
      from_name: 'Sistema Teste',
      message: 'Teste de configuração do EmailJS para o Chá de Panela',
      test_date: new Date().toLocaleString('pt-BR')
    }

    console.log('Parâmetros de teste:', testParams)

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID_GUEST, // Usar o template de convidado para teste
      testParams,
      EMAILJS_PUBLIC_KEY
    )

    console.log('Teste de email bem-sucedido:', response)
    return { success: true, response }
  } catch (error) {
    console.error('Erro detalhado no teste de email:', error)
    return { success: false, error }
  }
}

// Função para debug das configurações
export const debugEmailConfig = () => {
  console.log('=== DEBUG EmailJS Configuration ===')
  console.log('Service ID:', EMAILJS_SERVICE_ID || 'NÃO DEFINIDO')
  console.log('Template Guest ID:', EMAILJS_TEMPLATE_ID_GUEST || 'NÃO DEFINIDO')
  console.log('Template Gift ID:', EMAILJS_TEMPLATE_ID_GIFT || 'NÃO DEFINIDO')
  console.log('Public Key:', EMAILJS_PUBLIC_KEY || 'NÃO DEFINIDO')
  console.log('EmailJS inicializado:', !!emailjs)
  console.log('===================================')
}
