// Script para popular o Firestore com dados iniciais
// Execute: node firestore-seed.js

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, Timestamp } = require('firebase/firestore');

// Configure suas credenciais do Firebase aqui
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "cha-de-panela-d57dc.firebaseapp.com",
  projectId: "cha-de-panela-d57dc",
  storageBucket: "cha-de-panela-d57dc.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "1:1084361485509:web:f703d88a9a1731d7a194ac"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const sampleGifts = [
  {
    name: "Panela de Pressão",
    description: "Panela de pressão 5 litros, antiaderente",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "<PERSON><PERSON>",
    description: "Conjunto com 5 panelas antiaderentes",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Liquidificador",
    description: "Liquidificador 3 velocidades, 2 litros",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Micro-ondas",
    description: "Micro-ondas 30 litros com grill",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Cafeteira",
    description: "Cafeteira elétrica programável",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Mixer",
    description: "Mixer com 3 velocidades e copo",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Fritadeira Elétrica",
    description: "Air fryer 3.5 litros",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo de Facas",
    description: "Kit com 6 facas e suporte de madeira",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Batedeira Planetária",
    description: "Batedeira com 8 velocidades e tigela de inox",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Grill e Sanduicheira",
    description: "Aparelho 2 em 1 com placas antiaderentes",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Chaleira Elétrica",
    description: "Chaleira com capacidade para 1.8 litros e desligamento automático",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Aparelho de Jantar",
    description: "Conjunto de jantar com 20 peças para 4 pessoas",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo de Copos",
    description: "Conjunto com 6 copos de vidro para água/suco",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo de Taças",
    description: "Conjunto com 6 taças de cristal para vinho",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Faqueiro",
    description: "Faqueiro de aço inox com 42 peças",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Escorredor de Louça",
    description: "Escorredor de louça em inox com porta-talheres",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Conjunto de Potes Herméticos",
    description: "Kit com 10 potes de vidro com tampa hermética",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Tábua de Corte",
    description: "Tábua de corte de bambu grande",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Balança de Cozinha",
    description: "Balança digital de precisão para cozinha",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Ralador Multiuso",
    description: "Ralador com 4 faces em aço inox",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Espremedor de Frutas",
    description: "Espremedor de laranjas e limões elétrico",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo de Formas Antiaderentes",
    description: "Kit com 3 formas redondas para bolo",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Travessa de Vidro Refratária",
    description: "Travessa oval para forno e micro-ondas",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Galheteiro",
    description: "Conjunto para azeite, vinagre, sal e pimenta",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Porta Temperos",
    description: "Porta temperos giratório com 16 potes",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Pipoqueira Elétrica",
    description: "Pipoqueira sem óleo com bocal direcionador",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Moedor de Café",
    description: "Moedor de grãos de café elétrico",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Kit de Utensílios de Silicone",
    description: "Conjunto com escumadeira, concha, colher e espátula",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Cuscuzeira",
    description: "Panela para cuscuz individual em alumínio",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Wok",
    description: "Panela Wok com tampa e grelha",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jarra de Suco",
    description: "Jarra de vidro com capacidade para 2 litros",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Abridor de Latas e Garrafas",
    description: "Abridor multifuncional em aço inox",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Saladeira",
    description: "Saladeira grande de vidro com pegadores",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Fruteira de Mesa",
    description: "Fruteira de centro de mesa em aço cromado",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Descanso de Panela",
    description: "Kit com 4 descansos de panela de silicone",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Luva Térmica",
    description: "Par de luvas térmicas para cozinha",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Pano de Prato",
    description: "Conjunto com 7 panos de prato bordados",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Avental de Cozinha",
    description: "Avental divertido e moderno",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Lixeira para Pia",
    description: "Lixeira de 3 litros em inox para pia",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Rolo de Massa",
    description: "Rolo de massa em madeira com pontas giratórias",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Processador de Alimentos",
    description: "Mini processador de alimentos elétrico",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo Americano",
    description: "Conjunto com 4 lugares americanos impermeáveis",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Queijeira com Tampa",
    description: "Porta queijo com base de inox e tampa de acrílico",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Mantegueira",
    description: "Porta manteiga de cerâmica com tampa",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Jogo de Medidores",
    description: "Kit de xícaras e colheres medidoras",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Centrífuga de Saladas",
    description: "Secador de saladas manual",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Boleira com Tampa",
    description: "Prato para bolo com tampa de acrílico",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Amassador de Batatas",
    description: "Amassador de batatas em aço inox",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  },
  {
    name: "Garrafa Térmica",
    description: "Garrafa térmica de 1 litro com design moderno",
    image_url: "",
    is_reserved: false,
    reserved_by: null,
    reserved_at: null,
    message: null,
    created_at: Timestamp.now()
  }
];

async function seedFirestore() {
  try {
    console.log('🌱 Populando Firestore com dados iniciais...');
    
    for (const gift of sampleGifts) {
      const docRef = await addDoc(collection(db, 'gifts'), gift);
      console.log(`✅ Presente adicionado: ${gift.name} (ID: ${docRef.id})`);
    }
    
    console.log('🎉 Dados iniciais adicionados com sucesso!');
    console.log('📝 Agora você pode testar a aplicação com estes presentes de exemplo.');
    
  } catch (error) {
    console.error('❌ Erro ao popular Firestore:', error);
  }
}

seedFirestore();
