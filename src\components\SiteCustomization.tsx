'use client'

import { useState, useEffect } from 'react'
import { SiteCustomization } from '@/types'
import { getSiteCustomization, updateSiteCustomization } from '@/lib/api'
import { useNotification } from '@/hooks/useNotification'
import { Palette, Image, Eye, Save, Plus, Trash2, Link } from 'lucide-react'

interface SiteCustomizationProps {
  onUpdate: () => void
}

export default function SiteCustomizationComponent({ onUpdate }: SiteCustomizationProps) {
  const [customization, setCustomization] = useState<SiteCustomization | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [newImageUrl, setNewImageUrl] = useState('')
  const [validatingImage, setValidatingImage] = useState(false)

  const { showSuccess, showError, showInfo, NotificationComponent } = useNotification()
  
  const [formData, setFormData] = useState({
    background_images: [] as string[],
    background_style: 'slideshow' as 'slideshow' | 'static' | 'parallax',
    background_size: 'contain' as 'cover' | 'contain' | 'auto',
    overlay_opacity: 0.3,
    primary_color: '#ec4899',
    secondary_color: '#8b5cf6'
  })

  useEffect(() => {
    loadCustomization()
  }, [])

  const loadCustomization = async () => {
    try {
      setLoading(true)
      const data = await getSiteCustomization()
      if (data) {
        setCustomization(data)
        setFormData({
          background_images: data.background_images || [],
          background_style: data.background_style || 'slideshow',
          background_size: data.background_size || 'contain',
          overlay_opacity: data.overlay_opacity || 0.3,
          primary_color: data.primary_color || '#ec4899',
          secondary_color: data.secondary_color || '#8b5cf6'
        })
      }
    } catch (error) {
      showError('Erro ao Carregar', 'Não foi possível carregar as configurações de personalização.')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      await updateSiteCustomization(formData)
      await loadCustomization()
      onUpdate()
      showSuccess('Sucesso!', 'Personalização salva com sucesso!')
    } catch (error) {
      showError('Erro ao Salvar', 'Não foi possível salvar a personalização. Tente novamente.')
    } finally {
      setSaving(false)
    }
  }

  const validateAndAddImage = () => {
    const url = newImageUrl.trim()
    if (!url) return

    setValidatingImage(true)

    // Testa se a imagem carrega
    const img = document.createElement('img')

    img.onload = () => {
      // Se chegou aqui, a imagem carregou com sucesso
      setFormData({
        ...formData,
        background_images: [...formData.background_images, url]
      })
      setNewImageUrl('')
      setValidatingImage(false)
      showSuccess('Imagem Adicionada!', 'A imagem foi adicionada com sucesso à lista de backgrounds.')
    }

    img.onerror = () => {
      showError(
        'Erro ao Carregar Imagem',
        'Não foi possível carregar a imagem desta URL.\n\nVerifique se:\n• O link está correto\n• A imagem é pública\n• Use serviços como Imgur, PostImg ou ImgBB'
      )
      setValidatingImage(false)
    }

    img.src = url
  }

  const removeImage = (index: number) => {
    setFormData({
      ...formData,
      background_images: formData.background_images.filter((_, i) => i !== index)
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Carregando personalização...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center">
          <Palette className="w-6 h-6 mr-2" />
          Personalização Visual
        </h2>
        <button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 disabled:bg-gray-300 transition-colors"
        >
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'Salvando...' : 'Salvar'}
        </button>
      </div>

      {/* Imagens de Fundo */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Image className="w-5 h-5 mr-2" />
          Imagens de Fundo
        </h3>
        
        {/* Adicionar Nova Imagem */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <Link className="w-4 h-4 inline mr-2" />
            Adicionar Imagem por URL
          </label>
          <p className="text-sm text-gray-500 mb-3">
            Cole o link direto de uma imagem. <strong>Recomendado:</strong> Imgur, Postimg, ou ImgBB para melhor compatibilidade.
          </p>

          <div className="flex space-x-2">
            <input
              type="url"
              value={newImageUrl}
              onChange={(e) => setNewImageUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              placeholder="https://i.imgur.com/exemplo.jpg"
            />
            <button
              onClick={validateAndAddImage}
              disabled={!newImageUrl.trim() || validatingImage}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 transition-colors flex items-center"
            >
              {validatingImage ? (
                <>
                  <div className="w-4 h-4 mr-1 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Testando...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-1" />
                  Adicionar
                </>
              )}
            </button>
          </div>

          {/* Dicas de URLs */}
          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">💡 Serviços recomendados (gratuitos):</h4>
            <ul className="text-xs text-blue-700 space-y-2">
              <li>
                <strong>🥇 Imgur:</strong>
                <a href="https://imgur.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-1">
                  imgur.com
                </a>
                → Upload → Copiar link direto (.jpg/.png)
              </li>
              <li>
                <strong>🥈 PostImg:</strong>
                <a href="https://postimg.cc" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-1">
                  postimg.cc
                </a>
                → Upload → Copiar &ldquo;Direct link&rdquo;
              </li>
              <li>
                <strong>🥉 ImgBB:</strong>
                <a href="https://imgbb.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-1">
                  imgbb.com
                </a>
                → Upload → Copiar &ldquo;Direct link&rdquo;
              </li>
            </ul>
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-xs text-yellow-800">
                <strong>⚠️ Evite:</strong> Google Drive, Dropbox, OneDrive - têm restrições de CORS que impedem o carregamento.
              </p>
            </div>
          </div>
        </div>

        {/* Lista de Imagens */}
        <div className="space-y-3">
          {formData.background_images.length === 0 ? (
            <p className="text-gray-500 text-center py-4">
              Nenhuma imagem adicionada ainda. Adicione imagens para personalizar o fundo da página.
            </p>
          ) : (
            formData.background_images.map((url, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <img
                  src={url}
                  alt={`Imagem de fundo ${index + 1}`}
                  className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                  onError={(e) => {
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
                  }}
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 mb-1">
                    Imagem {index + 1}
                  </p>
                  <p className="text-xs text-gray-500 truncate" title={url}>
                    {url.length > 50 ? `${url.substring(0, 50)}...` : url}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <Link className="w-3 h-3 mr-1" />
                      URL Externa
                    </span>
                  </div>
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Visualizar imagem"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => removeImage(index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Remover imagem"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Estilo do Fundo */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Eye className="w-5 h-5 mr-2" />
          Configurações do Fundo
        </h3>

        {/* Estilo de Transição */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-700 mb-3">Estilo de Transição</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_style"
                value="slideshow"
                checked={formData.background_style === 'slideshow'}
                onChange={(e) => setFormData({ ...formData, background_style: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Slideshow</p>
                <p className="text-sm text-gray-500">Imagens alternam automaticamente</p>
              </div>
            </label>

            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_style"
                value="static"
                checked={formData.background_style === 'static'}
                onChange={(e) => setFormData({ ...formData, background_style: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Estático</p>
                <p className="text-sm text-gray-500">Primeira imagem fixa</p>
              </div>
            </label>

            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_style"
                value="parallax"
                checked={formData.background_style === 'parallax'}
                onChange={(e) => setFormData({ ...formData, background_style: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Parallax</p>
                <p className="text-sm text-gray-500">Efeito de profundidade</p>
              </div>
            </label>
          </div>
        </div>

        {/* Tamanho da Imagem */}
        <div>
          <h4 className="text-md font-medium text-gray-700 mb-3">Ajuste da Imagem</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_size"
                value="contain"
                checked={formData.background_size === 'contain'}
                onChange={(e) => setFormData({ ...formData, background_size: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Centralizada</p>
                <p className="text-sm text-gray-500">Imagem completa visível</p>
              </div>
            </label>

            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_size"
                value="cover"
                checked={formData.background_size === 'cover'}
                onChange={(e) => setFormData({ ...formData, background_size: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Preenchida</p>
                <p className="text-sm text-gray-500">Cobre toda a tela</p>
              </div>
            </label>

            <label className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="background_size"
                value="auto"
                checked={formData.background_size === 'auto'}
                onChange={(e) => setFormData({ ...formData, background_size: e.target.value as any })}
                className="text-pink-500"
              />
              <div>
                <p className="font-medium">Tamanho Real</p>
                <p className="text-sm text-gray-500">Tamanho original da imagem</p>
              </div>
            </label>
          </div>
        </div>
      </div>

      {/* Opacidade do Overlay */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Opacidade do Overlay
        </h3>
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            Opacidade: {Math.round(formData.overlay_opacity * 100)}%
          </label>
          <input
            type="range"
            min="0"
            max="0.8"
            step="0.1"
            value={formData.overlay_opacity}
            onChange={(e) => setFormData({ ...formData, overlay_opacity: parseFloat(e.target.value) })}
            className="w-full"
          />
          <p className="text-sm text-gray-500">
            Controla a transparência da camada escura sobre as imagens de fundo
          </p>
        </div>
      </div>

      {/* Cores do Tema */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Cores do Tema
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cor Primária
            </label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={formData.primary_color}
                onChange={(e) => setFormData({ ...formData, primary_color: e.target.value })}
                className="w-12 h-10 border border-gray-300 rounded-lg"
              />
              <input
                type="text"
                value={formData.primary_color}
                onChange={(e) => setFormData({ ...formData, primary_color: e.target.value })}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cor Secundária
            </label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={formData.secondary_color}
                onChange={(e) => setFormData({ ...formData, secondary_color: e.target.value })}
                className="w-12 h-10 border border-gray-300 rounded-lg"
              />
              <input
                type="text"
                value={formData.secondary_color}
                onChange={(e) => setFormData({ ...formData, secondary_color: e.target.value })}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Componente de Notificação */}
      <NotificationComponent />
    </div>
  )
}
