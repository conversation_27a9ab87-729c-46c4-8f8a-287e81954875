# 🔄 Migração do Supabase para Firebase

Este documento explica as principais mudanças feitas na migração do Supabase para Firebase.

## 📋 Resumo das Mudanças

### Dependências
- ❌ Removido: `@supabase/supabase-js`
- ✅ Adicionado: `firebase`

### Arquivos Modificados
- `package.json` - Dependências e scripts atualizados
- `src/lib/api.ts` - Migrado para Firestore
- `src/lib/firebase.ts` - Nova configuração do Firebase
- `.env.local.example` - Variáveis do Firebase
- `next.config.js` - Configurado para export estático

### Arquivos Adicionados
- `firebase.json` - Configuração do Firebase
- `firestore.rules` - Regras de segurança
- `firestore.indexes.json` - Índices do Firestore
- `.firebaserc` - Configuração do projeto
- `deploy.sh` - <PERSON>ript de deploy

### Arquivos Removidos
- `src/lib/supabase.ts`

## 🔧 Principais Diferenças Técnicas

### Estrutura de Dados

**Supabase (PostgreSQL)**
```sql
CREATE TABLE guests (
  id UUID PRIMARY KEY,
  name TEXT,
  total_people INTEGER,
  companions TEXT[],
  created_at TIMESTAMP
);
```

**Firebase (Firestore)**
```javascript
// Coleção: guests
{
  id: "auto-generated-id",
  name: "string",
  total_people: number,
  companions: ["array", "of", "strings"],
  created_at: Timestamp
}
```

### Consultas

**Supabase**
```javascript
const { data, error } = await supabase
  .from('guests')
  .select('*')
  .order('created_at', { ascending: false })
```

**Firebase**
```javascript
const q = query(
  collection(db, 'guests'), 
  orderBy('created_at', 'desc')
)
const querySnapshot = await getDocs(q)
```

### Inserção de Dados

**Supabase**
```javascript
const { data, error } = await supabase
  .from('guests')
  .insert([guestData])
  .select()
  .single()
```

**Firebase**
```javascript
const docRef = await addDoc(collection(db, 'guests'), {
  ...guestData,
  created_at: Timestamp.now()
})
```

## 🔒 Segurança

### Supabase (RLS)
```sql
ALTER TABLE guests ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public read" ON guests FOR SELECT USING (true);
```

### Firebase (Firestore Rules)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /guests/{document} {
      allow read, write: if true;
    }
  }
}
```

## 🚀 Deploy

### Supabase + Vercel
- Banco: Hospedado no Supabase
- Frontend: Vercel com deploy automático

### Firebase (Tudo Integrado)
- Banco: Firestore (Firebase)
- Frontend: Firebase Hosting
- Deploy: `firebase deploy`

## 💰 Custos

### Supabase
- **Gratuito**: 500MB de banco, 2GB de transferência
- **Pro**: $25/mês

### Firebase
- **Gratuito**: 1GB de armazenamento, 10GB de transferência
- **Blaze**: Pay-as-you-go

## 🎯 Vantagens da Migração

### Firebase
✅ **Integração Completa**: Banco + Hosting + Analytics em um lugar
✅ **Escalabilidade**: Auto-scaling do Google Cloud
✅ **Offline Support**: Sincronização automática
✅ **Real-time**: Updates em tempo real nativos
✅ **CDN Global**: Firebase Hosting com CDN mundial
✅ **Domínio Gratuito**: `.web.app` e `.firebaseapp.com`

### Supabase
✅ **SQL Familiar**: PostgreSQL padrão
✅ **APIs REST**: Geração automática de APIs
✅ **Tipagem**: Melhor integração com TypeScript
✅ **Backup**: Backups automáticos

## 🔄 Como Migrar Dados Existentes

Se você já tem dados no Supabase e quer migrar:

### 1. Exportar do Supabase
```sql
-- No SQL Editor do Supabase
SELECT * FROM guests;
SELECT * FROM gifts;
```

### 2. Converter para Firebase
```javascript
// Script de migração (exemplo)
import { db } from './src/lib/firebase'
import { collection, addDoc, Timestamp } from 'firebase/firestore'

const migrateData = async (supabaseData) => {
  for (const item of supabaseData) {
    await addDoc(collection(db, 'guests'), {
      ...item,
      created_at: Timestamp.fromDate(new Date(item.created_at))
    })
  }
}
```

## 🆘 Troubleshooting

### Erro: "Firebase not initialized"
- Verifique se todas as variáveis de ambiente estão configuradas
- Confirme se o arquivo `.env.local` está na raiz do projeto

### Erro: "Permission denied"
- Verifique se as regras do Firestore foram aplicadas
- Execute `firebase deploy --only firestore:rules`

### Erro de Build
- Confirme se `output: 'export'` está no `next.config.js`
- Verifique se não há APIs server-side sendo usadas

## 📞 Suporte

Para problemas específicos da migração:
1. Verifique os logs do console do navegador
2. Teste a conexão: `firebase projects:list`
3. Valide as regras: Firebase Console > Firestore > Rules
4. Monitore uso: Firebase Console > Usage
