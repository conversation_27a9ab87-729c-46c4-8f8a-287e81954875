'use client'

import { useState, useEffect } from 'react'
import { Gift } from '@/types'
import { getGifts } from '@/lib/api'
import ConfirmationForm from '@/components/ConfirmationForm'
import GiftList from '@/components/GiftList'
import CustomBackground from '@/components/CustomBackground'
import LocationMap from '@/components/LocationMap'
import { Heart, Gift as GiftIcon } from 'lucide-react'

export default function Home() {
  const [gifts, setGifts] = useState<Gift[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'confirmation' | 'gifts'>('confirmation')

  useEffect(() => {
    loadGifts()
  }, [])

  const loadGifts = async () => {
    try {
      const data = await getGifts()
      setGifts(data)
    } catch (error) {
      console.error('Erro ao carregar presentes:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGiftReserved = () => {
    loadGifts() // Recarrega a lista após reserva
  }

  return (
    <CustomBackground>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <Heart className="w-12 h-12 text-pink-500" />
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            Chá de Panela
          </h1>
          <h2 className="text-2xl font-semibold text-pink-600 mb-2">
            Daiane & Delandi Lucas
          </h2>

          {/* Data do Evento - Minimalista */}
          <div className="text-center mb-6">
            <p className="text-gray-600 text-lg">
              26 de Julho • 19:30h
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 mb-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">📍 Local do Evento</h3>
            <p className="text-gray-700 mb-2">
              <strong>Endereço:</strong> Prédio Residencial Hiate, nº 77
            </p>
            <p className="text-gray-700 mb-4">
              <strong>CEP:</strong> 29303-370
            </p>

            <h3 className="text-lg font-semibold text-gray-800 mb-3">🎨 Nossa Paleta de Cores</h3>
            <p className="text-gray-700 mb-3">
              Estamos montando nossa cozinha com muito carinho! Nossa preferência de cores:
            </p>
            <div className="flex justify-center gap-6 mb-4">
              {/* Cinza Claro */}
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-gray-300 rounded-full border-2 border-gray-400 shadow-md"></div>
                <span className="text-xs font-medium text-gray-700 mt-2">Cinza Claro</span>
              </div>

              {/* Bege */}
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full border-2 border-gray-400 shadow-md" style={{backgroundColor: '#d5cbc2'}}></div>
                <span className="text-xs font-medium text-gray-700 mt-2">Bege</span>
              </div>

              {/* Preto */}
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-black rounded-full border-2 border-gray-600 shadow-md"></div>
                <span className="text-xs font-medium text-gray-700 mt-2">Preto</span>
              </div>

              {/* Madeira */}
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full border-2 border-amber-600 shadow-md overflow-hidden"
                     style={{
                       backgroundImage: `url('https://allmadmadeiras.com.br/wp-content/uploads/2022/02/20220518_121131_2.jpg')`,
                       backgroundSize: 'cover',
                       backgroundPosition: 'center'
                     }}>
                </div>
                <span className="text-xs font-medium text-gray-700 mt-2">Madeira</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 italic">
              ✨ <strong>Preferência especial:</strong> Produtos em Inox e Vidros, são sempre bem-vindos!
            </p>
          </div>
          <p className="text-lg text-gray-600">
            Confirme sua presença e escolha um presente especial para nós! 💕
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-md">
            {/* Mobile: Vertical Stack, Desktop: Horizontal */}
            <div className="flex flex-col md:flex-row">
              <button
                onClick={() => setActiveTab('confirmation')}
                className={`px-4 py-3 md:px-6 rounded-md font-medium transition-colors text-center ${
                  activeTab === 'confirmation'
                    ? 'bg-pink-500 text-white'
                    : 'text-gray-600 hover:text-pink-500'
                }`}
              >
                <Heart className="w-5 h-5 inline mr-2" />
                Confirmar Presença
              </button>
              <button
                onClick={() => setActiveTab('gifts')}
                className={`px-4 py-3 md:px-6 rounded-md font-medium transition-colors text-center ${
                  activeTab === 'gifts'
                    ? 'bg-pink-500 text-white'
                    : 'text-gray-600 hover:text-pink-500'
                }`}
              >
                <GiftIcon className="w-5 h-5 inline mr-2" />
                Lista de Presentes
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          {activeTab === 'confirmation' && (
            <ConfirmationForm
              onSwitchToGifts={() => setActiveTab('gifts')}
            />
          )}
          {activeTab === 'gifts' && (
            <GiftList
              gifts={gifts}
              loading={loading}
              onGiftReserved={handleGiftReserved}
            />
          )}
        </div>

        {/* Localização do Evento */}
        <div className="mt-12 max-w-2xl mx-auto">
          <LocationMap
            address="Residencial Hiate, nº 77"
            cep="29303-370"
          />
        </div>

        {/* Footer */}
        <footer className="mt-16 py-8 border-t border-gray-200">
          <div className="text-center">
            <p className="text-gray-500 text-sm">
              Sistema desenvolvido por{' '}
              <span className="font-bold text-gray-700">DLTECHNOLOGY</span>
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Criando soluções digitais para momentos especiais ✨
            </p>
          </div>
        </footer>
      </div>
    </CustomBackground>
  )
}
