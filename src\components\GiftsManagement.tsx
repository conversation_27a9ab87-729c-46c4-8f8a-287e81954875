'use client'

import { useState } from 'react'
import { Gift } from '@/types'
import { createGift, updateGift, deleteGift } from '@/lib/api'
import { useNotification } from '@/hooks/useNotification'
import { Plus, Edit, Trash2, Gift as GiftIcon, X } from 'lucide-react'

interface GiftsManagementProps {
  gifts: Gift[]
  onUpdate: () => void
}

export default function GiftsManagement({ gifts, onUpdate }: GiftsManagementProps) {
  const [showForm, setShowForm] = useState(false)
  const [editingGift, setEditingGift] = useState<Gift | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image_url: '',
    purchase_url: ''
  })
  const [loading, setLoading] = useState(false)

  const { showSuccess, showError, showWarning, NotificationComponent } = useNotification()

  const resetForm = () => {
    setFormData({ name: '', description: '', image_url: '', purchase_url: '' })
    setEditingGift(null)
    setShowForm(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (editingGift) {
        await updateGift(editingGift.id, formData)
      } else {
        await createGift(formData)
      }
      onUpdate()
      resetForm()
      showSuccess('Sucesso!', editingGift ? 'Presente atualizado com sucesso!' : 'Presente adicionado com sucesso!')
    } catch (error) {
      showError('Erro ao Salvar', 'Não foi possível salvar o presente. Tente novamente.')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (gift: Gift) => {
    setEditingGift(gift)
    setFormData({
      name: gift.name,
      description: gift.description || '',
      image_url: gift.image_url || '',
      purchase_url: gift.purchase_url || ''
    })
    setShowForm(true)
  }

  const handleDelete = async (gift: Gift) => {
    if (gift.is_reserved) {
      showWarning('Não é Possível Excluir', 'Não é possível excluir um presente que já foi reservado.')
      return
    }

    if (confirm(`Tem certeza que deseja excluir o presente "${gift.name}"?`)) {
      try {
        await deleteGift(gift.id)
        onUpdate()
        showSuccess('Presente Excluído!', `O presente "${gift.name}" foi excluído com sucesso.`)
      } catch (error) {
        showError('Erro ao Excluir', 'Não foi possível excluir o presente. Tente novamente.')
      }
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          Gerenciar Lista de Presentes
        </h3>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Adicionar Presente
        </button>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-semibold text-gray-800">
                {editingGift ? 'Editar Presente' : 'Adicionar Presente'}
              </h4>
              <button
                onClick={resetForm}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Presente *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Ex: Panela de pressão"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Descrição opcional do presente"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL da Imagem
                </label>
                <input
                  type="url"
                  value={formData.image_url}
                  onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="https://exemplo.com/imagem.jpg"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link de Compra
                </label>
                <input
                  type="url"
                  value={formData.purchase_url}
                  onChange={(e) => setFormData({ ...formData, purchase_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="https://loja.com/produto"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Link onde os convidados podem comprar este presente online
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={resetForm}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={loading || !formData.name.trim()}
                  className="flex-1 bg-pink-500 text-white px-4 py-2 rounded-lg hover:bg-pink-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? 'Salvando...' : editingGift ? 'Atualizar' : 'Adicionar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Gifts List */}
      {gifts.length === 0 ? (
        <div className="text-center py-8">
          <GiftIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">Nenhum presente cadastrado ainda.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {gifts.map((gift) => (
            <div
              key={gift.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              {gift.image_url && (
                <img
                  src={gift.image_url}
                  alt={gift.name}
                  className="w-full h-32 object-cover rounded-lg mb-3"
                />
              )}
              
              <h4 className="font-semibold text-lg text-gray-800 mb-2">
                {gift.name}
              </h4>
              
              {gift.description && (
                <p className="text-gray-600 text-sm mb-3">
                  {gift.description}
                </p>
              )}

              {gift.purchase_url && (
                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Link de compra:</p>
                  <div className="flex items-center space-x-2">
                    <a
                      href={gift.purchase_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-xs truncate flex-1"
                      title={gift.purchase_url}
                    >
                      {gift.purchase_url}
                    </a>
                    <button
                      onClick={() => window.open(gift.purchase_url, '_blank')}
                      className="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 border border-blue-300 rounded"
                      title="Testar link"
                    >
                      🔗
                    </button>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between mb-3">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  gift.is_reserved
                    ? 'bg-red-100 text-red-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {gift.is_reserved ? 'Reservado' : 'Disponível'}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(gift)}
                  className="flex-1 flex items-center justify-center px-3 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Editar
                </button>
                <button
                  onClick={() => handleDelete(gift)}
                  disabled={gift.is_reserved}
                  className="flex-1 flex items-center justify-center px-3 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Excluir
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Componente de Notificação */}
      <NotificationComponent />
    </div>
  )
}
