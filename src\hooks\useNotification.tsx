'use client'

import { useState, useCallback } from 'react'
import NotificationModal, { NotificationType } from '@/components/NotificationModal'

interface NotificationState {
  isOpen: boolean
  type: NotificationType
  title: string
  message: string
  autoClose: boolean
  autoCloseDelay: number
}

export function useNotification() {
  const [notification, setNotification] = useState<NotificationState>({
    isOpen: false,
    type: 'info',
    title: '',
    message: '',
    autoClose: false,
    autoCloseDelay: 3000
  })

  const showNotification = useCallback((
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      autoClose?: boolean
      autoCloseDelay?: number
    }
  ) => {
    const autoClose = options?.autoClose ?? (type === 'success' || type === 'info')
    const autoCloseDelay = options?.autoCloseDelay ?? 3000

    setNotification({
      isOpen: true,
      type,
      title,
      message,
      autoClose,
      autoCloseDelay
    })
  }, [])

  const hideNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, isOpen: false }))
  }, [])

  // Métodos de conveniência
  const showSuccess = useCallback((title: string, message: string, autoClose = true) => {
    showNotification('success', title, message, { autoClose })
  }, [showNotification])

  const showError = useCallback((title: string, message: string, autoClose = false) => {
    showNotification('error', title, message, { autoClose })
  }, [showNotification])

  const showInfo = useCallback((title: string, message: string, autoClose = true) => {
    showNotification('info', title, message, { autoClose })
  }, [showNotification])

  const showWarning = useCallback((title: string, message: string, autoClose = false) => {
    showNotification('warning', title, message, { autoClose })
  }, [showNotification])

  const NotificationComponent = useCallback(() => (
    <NotificationModal
      isOpen={notification.isOpen}
      type={notification.type}
      title={notification.title}
      message={notification.message}
      onClose={hideNotification}
      autoClose={notification.autoClose}
      autoCloseDelay={notification.autoCloseDelay}
    />
  ), [notification, hideNotification])

  return {
    showNotification,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    hideNotification,
    NotificationComponent
  }
}
