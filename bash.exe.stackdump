Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FE8E
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210286019, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB730  000210068E24 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA10  00021006A225 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE140D0000 ntdll.dll
7FFE12A60000 KERNEL32.DLL
7FFE114A0000 KERNELBASE.dll
7FFE127F0000 USER32.dll
7FFE11D30000 win32u.dll
000210040000 msys-2.0.dll
7FFE11E40000 GDI32.dll
7FFE11B60000 gdi32full.dll
7FFE11C90000 msvcp_win.dll
7FFE11300000 ucrtbase.dll
7FFE12DB0000 advapi32.dll
7FFE13AA0000 msvcrt.dll
7FFE129B0000 sechost.dll
7FFE11880000 bcrypt.dll
7FFE12430000 RPCRT4.dll
7FFE109B0000 CRYPTBASE.DLL
7FFE11420000 bcryptPrimitives.dll
7FFE122F0000 IMM32.DLL
