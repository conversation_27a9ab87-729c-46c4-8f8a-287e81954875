'use client'

import { useState, useEffect } from 'react'
import { SiteCustomization } from '@/types'
import { getSiteCustomization } from '@/lib/api'

interface CustomBackgroundProps {
  children: React.ReactNode
}

export default function CustomBackground({ children }: CustomBackgroundProps) {
  const [customization, setCustomization] = useState<SiteCustomization | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadCustomization()
  }, [])

  useEffect(() => {
    if (customization?.background_style === 'slideshow' && customization.background_images.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prev) => 
          (prev + 1) % customization.background_images.length
        )
      }, 5000) // Troca a cada 5 segundos

      return () => clearInterval(interval)
    }
  }, [customization])

  const loadCustomization = async () => {
    try {
      const data = await getSiteCustomization()
      setCustomization(data)
    } catch (error) {
      console.error('Erro ao carregar personalização:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
        {children}
      </div>
    )
  }

  if (!customization || customization.background_images.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
        {children}
      </div>
    )
  }

  const currentImage = customization.background_images[currentImageIndex] || customization.background_images[0]
  
  const backgroundStyle = {
    backgroundImage: `url('${currentImage}')`,
    backgroundSize: customization.background_size || 'contain',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: customization.background_style === 'parallax' ? 'fixed' : 'scroll'
  }

  const overlayStyle = {
    backgroundColor: `rgba(0, 0, 0, ${customization.overlay_opacity})`
  }

  return (
    <div className="min-h-screen relative">
      {/* Background Image */}
      <div 
        className="fixed inset-0 z-0 transition-all duration-1000 ease-in-out"
        style={backgroundStyle}
      />
      
      {/* Overlay */}
      <div 
        className="fixed inset-0 z-10"
        style={overlayStyle}
      />
      
      {/* Content */}
      <div className="relative z-20 min-h-screen">
        {children}
      </div>
      
      {/* Custom CSS Variables for Theme Colors */}
      <style jsx global>{`
        :root {
          --primary-color: ${customization.primary_color};
          --secondary-color: ${customization.secondary_color};
        }
        
        .bg-primary {
          background-color: var(--primary-color) !important;
        }
        
        .text-primary {
          color: var(--primary-color) !important;
        }
        
        .border-primary {
          border-color: var(--primary-color) !important;
        }
        
        .bg-secondary {
          background-color: var(--secondary-color) !important;
        }
        
        .text-secondary {
          color: var(--secondary-color) !important;
        }
        
        .border-secondary {
          border-color: var(--secondary-color) !important;
        }
        
        /* Override existing pink colors with custom primary */
        .bg-pink-500 {
          background-color: var(--primary-color) !important;
        }
        
        .hover\\:bg-pink-600:hover {
          background-color: color-mix(in srgb, var(--primary-color) 90%, black) !important;
        }
        
        .text-pink-600 {
          color: var(--primary-color) !important;
        }
        
        .text-pink-700 {
          color: color-mix(in srgb, var(--primary-color) 90%, black) !important;
        }
        
        .text-pink-800 {
          color: color-mix(in srgb, var(--primary-color) 80%, black) !important;
        }
        
        .border-pink-500 {
          border-color: var(--primary-color) !important;
        }
        
        .bg-pink-50 {
          background-color: color-mix(in srgb, var(--primary-color) 10%, white) !important;
        }
        
        .bg-pink-100 {
          background-color: color-mix(in srgb, var(--primary-color) 20%, white) !important;
        }
        
        .focus\\:ring-pink-500:focus {
          --tw-ring-color: var(--primary-color) !important;
        }
        
        /* Purple colors with secondary */
        .from-purple-50 {
          --tw-gradient-from: color-mix(in srgb, var(--secondary-color) 10%, white) !important;
        }
        
        .to-purple-50 {
          --tw-gradient-to: color-mix(in srgb, var(--secondary-color) 10%, white) !important;
        }
      `}</style>
    </div>
  )
}
