'use client'

import { MapPin, Navigation } from 'lucide-react'

interface LocationMapProps {
  address?: string
  cep?: string
  className?: string
}

export default function LocationMap({
  address = "Residencial Hiate, nº 77",
  cep = "29303-370",
  className = ""
}: LocationMapProps) {
  // Endereço completo para busca
  const fullAddress = `${address}, CEP ${cep}, Brasil`

  // URL para Google Maps
  const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(fullAddress)}`

  const handleOpenInGPS = () => {
    // Detectar se é mobile para abrir app nativo
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

    if (isMobile) {
      // Tentar abrir no app nativo do Google Maps
      window.open(googleMapsUrl, '_blank')
    } else {
      // Desktop: abrir no Google Maps web
      window.open(googleMapsUrl, '_blank')
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <MapPin className="w-4 h-4 text-pink-500 mr-2" />
            <div>
              <h3 className="text-sm font-semibold text-gray-800">Localização do Evento</h3>
              <p className="text-xs text-gray-600">{address} • CEP: {cep}</p>
            </div>
          </div>

          <button
            onClick={handleOpenInGPS}
            className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
          >
            <Navigation className="w-3 h-3 mr-1" />
            Abrir no GPS
          </button>
        </div>
      </div>
    </div>
  )
}
