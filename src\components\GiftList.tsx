'use client'

import { useState, useEffect } from 'react'
import { Gift } from '@/types'
import { reserveGift } from '@/lib/api'
import { sendGiftReservationEmail } from '@/lib/emailService'
import { useNotification } from '@/hooks/useNotification'
import { Gift as GiftIcon, Heart, X, User, MessageSquare, ZoomIn, Grid3X3, List, ExternalLink, ShoppingCart } from 'lucide-react'

interface GiftListProps {
  gifts: Gift[]
  loading: boolean
  onGiftReserved: () => void
}

export default function GiftList({ gifts, loading, onGiftReserved }: GiftListProps) {
  const [selectedGift, setSelectedGift] = useState<Gift | null>(null)
  const [reserverName, setReserverName] = useState('')
  const [message, setMessage] = useState('')
  const [reserving, setReserving] = useState(false)
  const [selectedImage, setSelectedImage] = useState<{ url: string; alt: string } | null>(null)
  const [viewMode, setViewMode] = useState<'standard' | 'compact'>('standard')

  const { showSuccess, showError, NotificationComponent } = useNotification()

  const handleReserveClick = (gift: Gift) => {
    setSelectedGift(gift)
    setReserverName('')
    setMessage('')
  }

  const handleImageClick = (imageUrl: string, giftName: string) => {
    setSelectedImage({ url: imageUrl, alt: giftName })
  }

  const closeImageModal = () => {
    setSelectedImage(null)
  }

  // Fechar modal com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && selectedImage) {
        closeImageModal()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [selectedImage])

  const handleReserveSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedGift || !reserverName.trim()) return

    setReserving(true)
    try {
      await reserveGift({
        gift_id: selectedGift.id,
        reserved_by: reserverName.trim(),
        message: message.trim() || undefined
      })

      // Enviar email de notificação
      try {
        await sendGiftReservationEmail({
          gift_name: selectedGift.name,
          reserved_by: reserverName.trim(),
          message: message.trim() || undefined,
          reservation_date: new Date().toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        })
      } catch (emailError) {
        console.error('Erro ao enviar email:', emailError)
        // Não falha a reserva se o email falhar
      }

      setSelectedGift(null)
      setReserverName('')
      setMessage('')
      onGiftReserved()

      showSuccess('Presente Reservado!', 'Seu presente foi reservado com sucesso! Obrigado por participar do nosso chá de panela! 💕')
    } catch (error) {
      showError('Erro ao Reservar', 'Não foi possível reservar o presente. Tente novamente.')
    } finally {
      setReserving(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Carregando presentes...</p>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-8">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4">
          <h2 className="text-xl md:text-2xl font-bold text-gray-800 text-center sm:text-left">
            Lista de Presentes
          </h2>

          {/* Botões de Visualização - Apenas Desktop */}
          <div className="hidden md:flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('standard')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'standard'
                  ? 'bg-pink-500 text-white'
                  : 'text-gray-600 hover:text-pink-500'
              }`}
            >
              <List className="w-4 h-4 inline mr-1" />
              Padrão
            </button>
            <button
              onClick={() => setViewMode('compact')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'compact'
                  ? 'bg-pink-500 text-white'
                  : 'text-gray-600 hover:text-pink-500'
              }`}
            >
              <Grid3X3 className="w-4 h-4 inline mr-1" />
              Compacto
            </button>
          </div>
        </div>

        {/* Como Funciona */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <h3 className="text-lg font-semibold text-green-800 mb-2">🎯 Como Funciona</h3>
          <div className="space-y-2 text-sm text-green-700">
            <p><strong>1. Reservar:</strong> Clique em &ldquo;Reservar Presente&rdquo; para garantir que ninguém mais dará o mesmo item</p>
            <p><strong>2. Comprar:</strong> Use o link &ldquo;Ver na Loja&rdquo; (quando disponível) para comprar online facilmente</p>
            {/* <p><strong>3. Evitar duplicatas:</strong> O sistema garante que cada presente seja dado apenas uma vez!</p> */}
          </div>
        </div>

        {/* Dica de Cores */}
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-4 mb-6 border-l-4 border-pink-500">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">🎨 Dica Especial</h3>
          <p className="text-gray-700 text-sm mb-3">
            Estamos montando nossa cozinha com muito carinho! Se você for comprar algo que não está na lista,
            nossa paleta de cores preferida é:
          </p>
          <div className="flex justify-center gap-4">
            {/* Cinza Claro */}
            <div className="flex flex-col items-center">
              <div className="w-6 h-6 bg-gray-300 rounded-full border border-gray-400"></div>
              <span className="text-xs text-gray-600 mt-1">Cinza Claro</span>
            </div>

            {/* Bege */}
            <div className="flex flex-col items-center">
              <div className="w-6 h-6 rounded-full border border-gray-400" style={{backgroundColor: '#d5cbc2'}}></div>
              <span className="text-xs text-gray-600 mt-1">Bege</span>
            </div>

            {/* Preto */}
            <div className="flex flex-col items-center">
              <div className="w-6 h-6 bg-black rounded-full border border-gray-600"></div>
              <span className="text-xs text-gray-600 mt-1">Preto</span>
            </div>

            {/* Madeira */}
            <div className="flex flex-col items-center">
              <div className="w-6 h-6 rounded-full border border-amber-600 overflow-hidden"
                   style={{
                     backgroundImage: `url('https://allmadmadeiras.com.br/wp-content/uploads/2022/02/20220518_121131_2.jpg')`,
                     backgroundSize: 'cover',
                     backgroundPosition: 'center'
                   }}>
              </div>
              <span className="text-xs text-gray-600 mt-1">Madeira</span>
            </div>
          </div>
        </div>
        
        {gifts.length === 0 ? (
          <div className="text-center py-8">
            <GiftIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Nenhum presente cadastrado ainda.</p>
          </div>
        ) : (
          <div className={`grid ${
            // Mobile sempre compacto (2 colunas)
            // Desktop: padrão (3 colunas) ou compacto (5 colunas)
            viewMode === 'compact'
              ? 'grid-cols-2 md:grid-cols-5 gap-3'
              : 'grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6'
          }`}>
            {gifts.map((gift) => (
              <div
                key={gift.id}
                className={`border border-gray-200 rounded-lg hover:shadow-md transition-shadow ${
                  // Mobile sempre compacto, Desktop depende do modo
                  viewMode === 'compact' ? 'p-2 md:p-2' : 'p-2 md:p-4'
                }`}
              >
                {gift.image_url && (
                  <div
                    className="relative group cursor-pointer mb-2 md:mb-4"
                    onClick={() => handleImageClick(gift.image_url!, gift.name)}
                  >
                    <img
                      src={gift.image_url}
                      alt={gift.name}
                      className={`w-full object-cover rounded-lg transition-transform hover:scale-105 ${
                        // Mobile sempre compacto, Desktop depende do modo
                        viewMode === 'compact' ? 'h-24 md:h-32' : 'h-24 md:h-48'
                      }`}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center pointer-events-none">
                      <ZoomIn className={`text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                        viewMode === 'compact' ? 'w-4 h-4 md:w-4 md:h-4' : 'w-4 h-4 md:w-8 md:h-8'
                      }`} />
                    </div>
                  </div>
                )}

                <h3 className={`font-semibold text-gray-800 ${
                  // Mobile sempre compacto, Desktop depende do modo
                  viewMode === 'compact' ? 'text-xs md:text-xs mb-1 line-clamp-2' : 'text-xs md:text-lg mb-1 md:mb-2 line-clamp-2 md:line-clamp-none'
                }`}>
                  {gift.name}
                </h3>

                {gift.description && (
                  <p className={`text-gray-600 mb-2 md:mb-4 ${
                    // Mobile sempre mostra descrição compacta, Desktop depende do modo
                    viewMode === 'compact' ? 'text-xs md:text-xs line-clamp-2' : 'text-xs md:text-sm line-clamp-2 md:line-clamp-none'
                  }`}>
                    {gift.description}
                  </p>
                )}

                <div className="space-y-1 md:space-y-2">
                  {gift.is_reserved ? (
                    <div className="text-center">
                      <span
                        className="inline-flex items-center px-2 py-1 md:px-3 md:py-2 rounded-full text-xs md:text-sm font-medium bg-red-100 text-red-800"
                        title="Este presente já foi reservado por outro convidado"
                      >
                        ❌ Já Reservado
                      </span>
                    </div>
                  ) : (
                    <div className="text-center space-y-1 md:space-y-2">
                      <span
                        className="inline-flex items-center px-2 py-1 md:px-3 md:py-2 rounded-full text-xs md:text-sm font-medium bg-green-100 text-green-800"
                        title="Este presente está disponível para reserva"
                      >
                        ✅ Disponível
                      </span>

                      {/* Botão Principal de Reserva */}
                      <button
                        onClick={() => handleReserveClick(gift)}
                        className={`w-full bg-pink-500 text-white rounded-lg font-medium hover:bg-pink-600 transition-colors ${
                          // Mobile sempre compacto, Desktop depende do modo
                          viewMode === 'compact'
                            ? 'px-2 py-1 text-xs md:px-3 md:py-2 md:text-xs'
                            : 'px-2 py-1 text-xs md:px-4 md:py-3 md:text-sm'
                        }`}
                        title="Reservar este presente para evitar duplicidade"
                      >
                        {viewMode === 'compact' ? (
                          // Mobile: ícone + texto compacto, Desktop compacto: só ícone
                          <>
                            <Heart className="w-3 h-3 inline mr-1 md:mr-0" />
                            <span className="md:hidden">Reservar</span>
                          </>
                        ) : (
                          // Desktop padrão: texto completo
                          <>
                            <Heart className="w-3 h-3 md:w-4 md:h-4 inline mr-1 md:mr-2" />
                            <span className="hidden md:inline">Reservar Presente</span>
                            <span className="md:hidden">Reservar</span>
                          </>
                        )}
                      </button>

                      {/* Link de Compra Secundário */}
                      {gift.purchase_url && (
                        <a
                          href={gift.purchase_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`w-full bg-gray-100 text-gray-700 border border-gray-300 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center ${
                            viewMode === 'compact'
                              ? 'px-2 py-1 text-xs md:px-3 md:py-1 md:text-xs'
                              : 'px-2 py-1 text-xs md:px-4 md:py-2 md:text-sm'
                          }`}
                          title="Link sugerido para compra online"
                        >
                          {viewMode === 'compact' ? (
                            <>
                              <ExternalLink className="w-3 h-3 inline mr-1 md:mr-0" />
                              <span className="md:hidden">Link</span>
                            </>
                          ) : (
                            <>
                              <ExternalLink className="w-3 h-3 md:w-4 md:h-4 inline mr-1 md:mr-2" />
                              <span className="hidden md:inline">Ver na Loja</span>
                              <span className="md:hidden">Link</span>
                            </>
                          )}
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal de Imagem */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50">
          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            {/* Botão Fechar */}
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200"
            >
              <X className="w-6 h-6" />
            </button>

            {/* Imagem */}
            <img
              src={selectedImage.url}
              alt={selectedImage.alt}
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              onClick={closeImageModal}
            />

            {/* Overlay clicável para fechar */}
            <div
              className="absolute inset-0 -z-10"
              onClick={closeImageModal}
            />

            {/* Título da imagem */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg">
              <p className="text-sm font-medium">{selectedImage.alt}</p>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Reserva */}
      {selectedGift && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Reservar Presente
              </h3>
              <button
                onClick={() => setSelectedGift(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="mb-4">
              {/* Cabeçalho do Modal */}
              <div className="p-4 bg-pink-50 border border-pink-200 rounded-lg mb-4">
                <h4 className="font-semibold text-pink-800 mb-2">🎁 Reservar Presente</h4>
                <p className="text-sm text-pink-700">
                  Ao reservar, você garante que ninguém mais dará o mesmo presente, evitando duplicatas!
                </p>
              </div>

              {/* Informações do Presente */}
              <div className="p-3 bg-gray-50 rounded-lg mb-4">
                <p className="font-medium text-gray-800">{selectedGift.name}</p>
                {selectedGift.description && (
                  <p className="text-sm text-gray-600 mt-1">{selectedGift.description}</p>
                )}
              </div>

              {/* Link de Compra no Modal */}
              {selectedGift.purchase_url && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                  <p className="text-sm text-blue-800 mb-2">
                    💡 <strong>Dica:</strong> Após reservar, você pode comprar neste link sugerido:
                  </p>
                  <a
                    href={selectedGift.purchase_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Ver na Loja Online
                  </a>
                </div>
              )}
            </div>
            
            <form onSubmit={handleReserveSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Seu nome completo
                </label>
                <input
                  type="text"
                  value={reserverName}
                  onChange={(e) => setReserverName(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Digite seu nome"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MessageSquare className="w-4 h-4 inline mr-2" />
                  Mensagem (opcional)
                </label>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Deixe uma mensagem carinhosa..."
                />
              </div>
              
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setSelectedGift(null)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={reserving || !reserverName.trim()}
                  className="flex-1 bg-pink-500 text-white px-4 py-2 rounded-lg hover:bg-pink-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {reserving ? 'Reservando...' : 'Reservar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Componente de Notificação */}
      <NotificationComponent />
    </>
  )
}
