# 🚀 Guia de Configuração Rápida - Firebase

## 1. Configurar Firebase

### Criar Projeto
1. Acesse [firebase.google.com](https://firebase.google.com)
2. Clique em "Ir para o console"
3. Clique em "Criar um projeto"
4. Escolha um nome para o projeto (ex: "cha-de-panela")
5. Configure o Google Analytics (opcional)
6. Clique em "Criar projeto"

### Configurar Firestore
1. No console do Firebase, vá para "Firestore Database"
2. Clique em "Criar banco de dados"
3. Escolha "Iniciar no modo de teste" (por enquanto)
4. Selecione uma localização próxima
5. Clique em "Concluído"

### Configurar Hosting
1. No console do Firebase, vá para "Hosting"
2. Clique em "Começar"
3. <PERSON>ga as instruções (vamos configurar via CLI)

### Obter Credenciais
1. No console do Firebase, vá para "Configurações do projeto" (ícone de engrenagem)
2. <PERSON> aba "Geral", role até "Seus aplicativos"
3. Clique em "Adicionar app" > "Web" (ícone </>)
4. Registre o app com um nome
5. Copie as credenciais de configuração

### Configurar Regras do Firestore
1. No console do Firebase, vá para "Firestore Database"
2. Clique na aba "Regras"
3. Substitua as regras existentes por:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read and write access to guests collection
    match /guests/{document} {
      allow read, write: if true;
    }

    // Allow read and write access to gifts collection
    match /gifts/{document} {
      allow read, write: if true;
    }
  }
}
```

4. Clique em "Publicar"

**Nota:** Essas regras permitem acesso público para simplificar. Em produção, considere regras mais restritivas.

## 2. Configurar Variáveis de Ambiente

1. Copie o arquivo `.env.local.example` para `.env.local`
2. Edite o arquivo `.env.local` com suas credenciais:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=sua_api_key_aqui
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=seu_projeto.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=seu_project_id_aqui
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=seu_projeto.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=seu_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=seu_app_id_aqui
ADMIN_PASSWORD=sua_senha_admin_aqui
```

## 3. Instalar Firebase CLI e Configurar

```bash
# Instalar Firebase CLI globalmente
npm install -g firebase-tools

# Fazer login no Firebase
firebase login

# Instalar dependências do projeto
npm install

# Inicializar Firebase no projeto
firebase init

# Selecione:
# - Firestore: Configure security rules and indexes files
# - Hosting: Configure files for Firebase Hosting
#
# Use as configurações existentes quando perguntado
```

## 4. Configurar Projeto Firebase

1. Edite o arquivo `.firebaserc` e substitua `your-firebase-project-id` pelo ID do seu projeto
2. Execute `firebase deploy --only firestore:rules` para aplicar as regras

## 5. Executar Localmente

```bash
# Executar em modo desenvolvimento
npm run dev
```

## 6. Deploy para Produção

```bash
# Build e deploy automático
npm run deploy

# Ou manualmente
npm run build
firebase deploy --only hosting
```

Sua aplicação estará disponível em: `https://seu-project-id.web.app`

## 7. Testar a Aplicação

### Página Principal (Convidados)
- Acesse: `http://localhost:3000`
- Teste a confirmação de presença
- Teste a visualização da lista de presentes

### Painel Administrativo
- Acesse: `http://localhost:3000/admin`
- Use a senha definida no `.env.local`
- Teste todas as funcionalidades:
  - Adicionar presentes
  - Ver convidados
  - Exportar dados

## 5. Adicionar Presentes Iniciais (Opcional)

No painel administrativo, adicione alguns presentes de exemplo:

1. **Panela de Pressão**
   - Descrição: "Panela de pressão 5 litros"
   
2. **Jogo de Panelas**
   - Descrição: "Conjunto com 5 panelas antiaderentes"
   
3. **Liquidificador**
   - Descrição: "Liquidificador 3 velocidades"

4. **Micro-ondas**
   - Descrição: "Micro-ondas 30 litros"

5. **Cafeteira**
   - Descrição: "Cafeteira elétrica programável"

## ✅ Checklist de Validação

- [ ] Firebase configurado (Firestore + Hosting)
- [ ] Variáveis de ambiente configuradas
- [ ] Firebase CLI configurado e logado
- [ ] Regras do Firestore aplicadas
- [ ] Aplicação rodando em localhost:3000
- [ ] Confirmação de presença funcionando
- [ ] Lista de presentes carregando
- [ ] Reserva de presentes funcionando
- [ ] Login no painel admin funcionando
- [ ] Gerenciamento de presentes funcionando
- [ ] Exportação para Excel funcionando
- [ ] Interface responsiva em mobile
- [ ] Deploy para Firebase Hosting funcionando

## 🎯 Próximos Passos

1. **Personalizar Design**: Ajuste cores e textos conforme o tema do evento
2. **Adicionar Presentes**: Cadastre todos os presentes desejados
3. **Testar Completamente**: Faça testes em diferentes dispositivos
4. **Deploy**: Publique no Firebase Hosting
5. **Configurar Domínio Personalizado** (opcional): Configure um domínio próprio no Firebase
6. **Compartilhar**: Envie o link para os convidados

## 🆘 Problemas Comuns

### Erro de Conexão com Firebase
- Verifique se as credenciais do Firebase estão corretas
- Confirme se o Firestore está ativado
- Verifique se as regras do Firestore foram aplicadas
- Teste a conexão com `firebase projects:list`

### Página em Branco
- Verifique o console do navegador para erros
- Confirme se todas as dependências foram instaladas
- Reinicie o servidor de desenvolvimento

### Erro de Autenticação Admin
- Verifique se a senha no `.env.local` está correta
- Confirme se o arquivo `.env.local` está na raiz do projeto
