export function verifyAdminPassword(password: string): boolean {
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'
  return password === adminPassword
}

export function setAuthToken() {
  if (typeof window !== 'undefined') {
    localStorage.setItem('admin_authenticated', 'true')
  }
}

export function removeAuthToken() {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('admin_authenticated')
  }
}

export function isAuthenticated(): boolean {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('admin_authenticated') === 'true'
  }
  return false
}
