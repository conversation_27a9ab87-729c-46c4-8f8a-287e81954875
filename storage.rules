rules_version = '2';

// Craft rules based on data in your Firestore database
// allow write: if firestore.get(
//    /databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin;
service firebase.storage {
  match /b/{bucket}/o {
    // Allow read access to all files
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // Allow write access to background-images folder
    match /background-images/{imageId} {
      allow write, delete: if true;
    }
    
    // Allow write access to gift-images folder (for future use)
    match /gift-images/{imageId} {
      allow write, delete: if true;
    }
  }
}
